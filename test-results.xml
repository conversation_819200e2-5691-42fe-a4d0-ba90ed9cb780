<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="10" time="2.666" timestamp="2025-10-07T15:50:18.393012" hostname="HYB-d7HUHw58Q86"><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_success" time="0.018" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_duplicate" time="0.006" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_failure" time="0.017" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_batch_events" time="0.023" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_success" time="0.017" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_duplicates" time="0.006" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_failures" time="0.011" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_sns_wrapped_message" time="0.023" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_exception" time="0.034" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_multiple_records" time="0.007" /></testsuite></testsuites>