-- Fix Database User for IAM Authentication
-- Run these commands on your Aurora PostgreSQL database

-- 1. Drop the existing user if it exists (it was created with password)
DROP USER IF EXISTS au_sa_ambulance_user;

-- 2. Create user for IAM authentication (no password needed)
CREATE USER au_sa_ambulance_user;

-- 3. Grant the rds_iam role to enable IAM authentication
GRANT rds_iam TO au_sa_ambulance_user;

-- 4. <PERSON> schema usage permissions
GRANT USAGE ON SCHEMA "au-sa-ambulance" TO au_sa_ambulance_user;

-- 5. Grant table permissions for existing tables
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "au-sa-ambulance" TO au_sa_ambulance_user;

-- 6. Grant sequence permissions for existing sequences
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA "au-sa-ambulance" TO au_sa_ambulance_user;

-- 7. Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA "au-sa-ambulance" 
GRANT SELECT, INSERT, UPDATE, <PERSON>LETE ON TABLES TO au_sa_ambulance_user;

-- 8. Set default privileges for future sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA "au-sa-ambulance"
GRANT USAGE, SELECT ON SEQUENCES TO au_sa_ambulance_user;

-- 9. Verify the user was created correctly
SELECT usename, usesuper, usecreatedb, usecanlogin 
FROM pg_user 
WHERE usename = 'au_sa_ambulance_user';

-- 10. Verify IAM role assignment
SELECT r.rolname 
FROM pg_roles r 
JOIN pg_auth_members m ON r.oid = m.roleid 
JOIN pg_roles u ON u.oid = m.member 
WHERE u.rolname = 'au_sa_ambulance_user' AND r.rolname = 'rds_iam';
